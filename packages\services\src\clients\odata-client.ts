/**
 * 基于 @odata/client 的 OData 客户端实现
 */

import { OData } from '@odata/client'
import { ODataV4 } from '@odata/client/lib/types_v4'
import {
  ApiResponse,
  IODataClient,
  ODataClientConfig,
  ODataEntity,
  ODataQueryOptions,
  RequestContext,
  ServiceError,
} from '../types'
import {
  ODataQueryBuilderOptions,
  buildODataQuery,
} from '../utils/odata-query-builder'

export class ODataClient implements IODataClient {
  private client: ODataV4
  private config: ODataClientConfig

  constructor(config: ODataClientConfig) {
    this.config = config

    // 创建 @odata/client 实例
    this.client = OData.New4({
      serviceEndpoint: config.baseUrl,
    })
  }

  async query<T = ODataEntity>(
    entitySet: string,
    options: ODataQueryOptions = {}
  ): Promise<ApiResponse<T>> {
    try {
      const builderOptions: ODataQueryBuilderOptions = {
        select: options.select,
        filter: options.filter,
        orderBy: options.orderby,
        top: options.top,
        skip: options.skip,
        count: options.count,
        expand: options.expand,
      }
      const queryString = buildODataQuery(builderOptions)

      const response = await this.client.newRequest({
        method: 'GET',
        collection: entitySet,
        // params: queryString.toString(),
      })
      return {
        data: response.value || response,
        success: true,
        total: response['@odata.count'],
      }
    } catch (error) {
      throw this.handleError(error, `查询实体集 ${entitySet} 失败`)
    }
  }

  async get<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    options: Pick<ODataQueryOptions, 'select' | 'expand'> = {},
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      console.log(`[OData] 获取实体: ${entitySet}(${key})`, { options })

      // 构建查询选项
      const systemOptions = this.client.newOptions()

      if (options.select) {
        systemOptions.select(options.select.split(','))
      }
      if (options.expand) {
        systemOptions.expand(options.expand)
      }

      // 执行查询
      const response = await this.client
        .getEntitySet(entitySet)
        .retrieve(key, systemOptions)

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `获取实体 ${entitySet}(${key}) 失败`)
    }
  }

  async create<T = ODataEntity>(
    entitySet: string,
    entity: Partial<T>,
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      console.log(`[OData] 创建实体: ${entitySet}`, entity)

      // 执行创建操作
      const response = await this.client.getEntitySet(entitySet).create(entity)

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `创建实体 ${entitySet} 失败`)
    }
  }

  async update<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    entity: Partial<T>,
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      console.log(`[OData] 更新实体: ${entitySet}(${key})`, entity)

      // 执行更新操作 (PATCH)
      await this.client.getEntitySet(entitySet).update(key, entity)

      // 获取更新后的实体
      const response = await this.client.getEntitySet(entitySet).retrieve(key)

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `更新实体 ${entitySet}(${key}) 失败`)
    }
  }

  async replace<T = ODataEntity>(
    entitySet: string,
    key: string | number,
    entity: T,
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      console.log(`[OData] 替换实体: ${entitySet}(${key})`, entity)

      // 执行替换操作 (PUT) - 使用 newRequest 直接调用
      const response = await this.client.newRequest({
        collection: entitySet,
        method: 'PUT',
        id: key,
        entity,
      })

      return {
        data: response,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `替换实体 ${entitySet}(${key}) 失败`)
    }
  }

  async delete(
    entitySet: string,
    key: string | number,
    context: RequestContext = {}
  ): Promise<ApiResponse<boolean>> {
    try {
      console.log(`[OData] 删除实体: ${entitySet}(${key})`)

      // 执行删除操作
      await this.client.getEntitySet(entitySet).delete(key)

      return {
        data: true,
        success: true,
      }
    } catch (error) {
      throw this.handleError(error, `删除实体 ${entitySet}(${key}) 失败`)
    }
  }

  /**
   * 使用原生 OData 查询字符串进行查询
   *
   * @example
   * // 基础查询
   * await client.queryWithString('Users', '$select=Id,Name&$top=10')
   *
   * // 使用 odata-query 构建查询（需要安装 odata-query 包）
   * import { buildQuery } from 'odata-query'
   * const query = buildQuery({
   *   select: ['Id', 'Name'],
   *   filter: { Name: { contains: 'John' } },
   *   top: 10
   * })
   * await client.queryWithString('Users', query)
   */
  async queryWithString<T = ODataEntity>(
    entitySet: string,
    queryString: string,
    context: RequestContext = {}
  ): Promise<ApiResponse<T>> {
    try {
      console.log(`[OData] 原生查询: ${entitySet}?${queryString}`)

      // 使用原生查询字符串 - 直接使用 newRequest
      const response = await this.client.newRequest({
        collection: entitySet,
        method: 'GET',
        params: this.client.newOptions().custom(queryString),
      })

      return {
        data: response.value || response,
        success: true,
        total: response['@odata.count'],
      }
    } catch (error) {
      throw this.handleError(error, `原生查询 ${entitySet} 失败`)
    }
  }

  private handleError(error: any, message: string): ServiceError {
    if (error instanceof ServiceError) {
      return error
    }

    return new ServiceError(
      `${message}: ${error.message || '未知错误'}`,
      error.status,
      'ODATA_ERROR',
      error
    )
  }
}

// 创建默认的 OData 客户端实例
let defaultODataClient: ODataClient | null = null

export function createODataClient(config: ODataClientConfig): ODataClient {
  return new ODataClient(config)
}

export function getDefaultODataClient(): ODataClient | null {
  return defaultODataClient
}

export function setDefaultODataClient(client: ODataClient): void {
  defaultODataClient = client
}
