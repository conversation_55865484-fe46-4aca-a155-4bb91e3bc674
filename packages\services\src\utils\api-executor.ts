/**
 * API 执行器
 */

import {
  ApiConfig,
  ApiResponse,
  ODataApiConfig,
  RequestContext,
  RestApiConfig,
  ServiceError,
} from '../types'
import { getDefaultAxiosClient } from '../clients/axios-client'
import { createODataClient } from '../clients/odata-client'
import { extractDataByPath, isODataConfig } from './common'

/**
 * 统一的 API 执行函数
 */
export async function executeApi(
  config: ApiConfig,
  context: RequestContext = {}
): Promise<ApiResponse> {
  try {
    console.log('[executeApi] 执行 API 请求:', config)

    // 根据协议类型选择对应的客户端
    if (isODataConfig(config)) {
      return await executeODataApi(config as ODataApiConfig, context)
    } else {
      return await executeRestApi(config as RestApiConfig, context)
    }
  } catch (error) {
    console.error('[executeApi] API 请求失败:', error)

    if (error instanceof ServiceError) {
      throw error
    }

    throw new ServiceError(
      `API 请求失败: ${error.message || '未知错误'}`,
      undefined,
      'API_ERROR',
      error
    )
  }
}

/**
 * 执行 REST API 请求
 */
async function executeRestApi(
  config: RestApiConfig,
  context: RequestContext
): Promise<ApiResponse> {
  const axiosClient = getDefaultAxiosClient()

  try {
    const response = await axiosClient.request(config)

    return {
      data: response,
      success: true,
    }
  } catch (error) {
    throw new ServiceError(
      `REST API 请求失败: ${error.message || '未知错误'}`,
      error.status,
      'REST_ERROR',
      error
    )
  }
}

/**
 * 执行 OData API 请求
 */
async function executeODataApi(
  config: ODataApiConfig,
  context: RequestContext
): Promise<ApiResponse> {
  // 从 URL 中提取 baseUrl
  const url = new URL(config.url)
  const baseUrl = `${url.protocol}//${url.host}${url.pathname
    .split('/')
    .slice(0, -1)
    .join('/')}`
  const entitySet = url.pathname.split('/').pop() || ''

  const odataClient = createODataClient({
    baseUrl,
    headers: config.headers,
    timeout: config.timeout,
  })

  try {
    // 合并上下文参数到 OData 查询选项
    const queryOptions = {
      ...config.odata,
      ...context.params,
    }

    const response = await odataClient.query(entitySet, queryOptions, context)

    // 如果指定了 dataPath，从响应中提取数据
    if (config.dataPath) {
      const extractedData = extractDataByPath(response.data, config.dataPath)
      return {
        ...response,
        data: extractedData,
      }
    }

    return response
  } catch (error) {
    throw new ServiceError(
      `OData API 请求失败: ${error.message || '未知错误'}`,
      error.status,
      'ODATA_ERROR',
      error
    )
  }
}

/**
 * 批量执行 API 请求
 */
export async function executeBatchApi(
  configs: ApiConfig[],
  context: RequestContext = {}
): Promise<ApiResponse[]> {
  try {
    console.log('[executeBatchApi] 批量执行 API 请求:', configs.length)

    const promises = configs.map(config => executeApi(config, context))
    const results = await Promise.allSettled(promises)

    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        console.error(`[executeBatchApi] 第 ${index + 1} 个请求失败:`, result.reason)
        return {
          data: null,
          success: false,
          error: result.reason.message || '请求失败',
        }
      }
    })
  } catch (error) {
    throw new ServiceError(
      `批量 API 请求失败: ${error.message || '未知错误'}`,
      undefined,
      'BATCH_API_ERROR',
      error
    )
  }
}

/**
 * 并发执行 API 请求（带限制）
 */
export async function executeConcurrentApi(
  configs: ApiConfig[],
  context: RequestContext = {},
  concurrency: number = 5
): Promise<ApiResponse[]> {
  try {
    console.log('[executeConcurrentApi] 并发执行 API 请求:', {
      total: configs.length,
      concurrency,
    })

    const results: ApiResponse[] = new Array(configs.length)
    const executing: Promise<void>[] = []

    for (let i = 0; i < configs.length; i++) {
      const promise = executeApi(configs[i], context)
        .then(result => {
          results[i] = result
        })
        .catch(error => {
          console.error(`[executeConcurrentApi] 第 ${i + 1} 个请求失败:`, error)
          results[i] = {
            data: null,
            success: false,
            error: error.message || '请求失败',
          }
        })

      executing.push(promise)

      // 控制并发数量
      if (executing.length >= concurrency) {
        await Promise.race(executing)
        executing.splice(executing.findIndex(p => p === promise), 1)
      }
    }

    // 等待所有剩余的请求完成
    await Promise.all(executing)

    return results
  } catch (error) {
    throw new ServiceError(
      `并发 API 请求失败: ${error.message || '未知错误'}`,
      undefined,
      'CONCURRENT_API_ERROR',
      error
    )
  }
}

/**
 * 带重试的 API 执行
 */
export async function executeApiWithRetry(
  config: ApiConfig,
  context: RequestContext = {},
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<ApiResponse> {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      return await executeApi(config, context)
    } catch (error) {
      lastError = error

      if (attempt <= maxRetries) {
        console.warn(
          `[executeApiWithRetry] 第 ${attempt} 次尝试失败，${retryDelay}ms 后重试:`,
          error.message
        )
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        retryDelay *= 2 // 指数退避
      }
    }
  }

  throw new ServiceError(
    `API 请求重试 ${maxRetries} 次后仍然失败: ${lastError.message}`,
    lastError.status,
    'RETRY_FAILED',
    lastError
  )
}
