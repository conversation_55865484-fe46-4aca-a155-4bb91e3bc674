#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/cli.cjs" "$@"
else
  exec node  "$basedir/../../../../../mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/cli.cjs" "$@"
fi
