/**
 * 简化版 Axios 客户端实现
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import {
  ApiClient,
  AxiosClientConfig,
  BaseRequestConfig,
  RestApiConfig,
  ServiceError,
} from '../types'

export class AxiosClient implements ApiClient {
  private axiosInstance: AxiosInstance

  constructor(config: AxiosClientConfig = {}) {
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
    })

    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      (config) => {
        console.log(
          `[Axios] 发送请求: ${config.method?.toUpperCase()} ${config.url}`
        )
        return config
      },
      (error) => {
        console.error('[Axios] 请求错误:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      (response) => {
        console.log(
          `[Axios] 响应成功: ${response.status} ${response.config.url}`
        )
        return response
      },
      (error) => {
        console.error('[Axios] 响应错误:', error)
        return Promise.reject(this.handleError(error))
      }
    )
  }

  async request<T = any>(
    config: BaseRequestConfig | RestApiConfig
  ): Promise<T> {
    try {
      const axiosConfig: AxiosRequestConfig = {
        url: config.url,
        method: config.method,
        headers: config.headers,
        timeout: config.timeout,
      }

      // 处理 REST API 特有的配置
      if ('params' in config && config.params) {
        axiosConfig.params = config.params
      }

      if ('body' in config && config.body) {
        axiosConfig.data = config.body
      }

      const response: AxiosResponse<T> = await this.axiosInstance.request(
        axiosConfig
      )

      // 如果指定了 dataPath，从响应中提取数据
      if ('dataPath' in config && config.dataPath) {
        return this.extractDataByPath(response.data, config.dataPath)
      }

      return response.data
    } catch (error) {
      throw this.handleError(error)
    }
  }

  private extractDataByPath(data: any, path: string): any {
    const keys = path.split('.')
    let result = data

    for (const key of keys) {
      if (result && typeof result === 'object' && key in result) {
        result = result[key]
      } else {
        throw new ServiceError(`无法在响应中找到路径: ${path}`)
      }
    }

    return result
  }

  private handleError(error: any): ServiceError {
    if (error instanceof ServiceError) {
      return error
    }

    if (axios.isAxiosError(error)) {
      const status = error.response?.status
      const message =
        error.response?.data?.message || error.message || '请求失败'
      const details = error.response?.data

      return new ServiceError(message, status, 'AXIOS_ERROR', details)
    }

    return new ServiceError(
      error.message || '未知错误',
      undefined,
      'UNKNOWN_ERROR',
      error
    )
  }

  // 便捷方法

  async query<T = any>(
    url: string,
    params?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'get',
      params,
      headers,
    } as RestApiConfig)
  }
  async get<T = any>(
    url: string,
    params?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'get',
      params,
      headers,
    } as RestApiConfig)
  }

  async post<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'post',
      body: data,
      headers,
    } as RestApiConfig)
  }

  async put<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'put',
      body: data,
      headers,
    } as RestApiConfig)
  }

  async patch<T = any>(
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'patch',
      body: data,
      headers,
    } as RestApiConfig)
  }

  async delete<T = any>(
    url: string,
    headers?: Record<string, string>
  ): Promise<T> {
    return this.request<T>({
      url,
      method: 'delete',
      headers,
    })
  }
}

// 创建默认的 Axios 客户端实例
let defaultAxiosClient: AxiosClient | null = null

export function createAxiosClient(config?: AxiosClientConfig): AxiosClient {
  return new AxiosClient(config)
}

export function getDefaultAxiosClient(): AxiosClient {
  if (!defaultAxiosClient) {
    defaultAxiosClient = new AxiosClient()
  }
  return defaultAxiosClient
}

export function setDefaultAxiosClient(client: AxiosClient): void {
  defaultAxiosClient = client
}
