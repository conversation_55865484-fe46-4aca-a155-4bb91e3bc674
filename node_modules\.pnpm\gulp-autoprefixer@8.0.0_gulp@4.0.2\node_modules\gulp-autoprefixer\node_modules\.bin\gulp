#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/gulp@4.0.2/node_modules/gulp/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/gulp@4.0.2/node_modules/gulp/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/gulp@4.0.2/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/gulp@4.0.2/node_modules/gulp/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/gulp@4.0.2/node_modules/gulp/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/gulp@4.0.2/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../gulp@4.0.2/node_modules/gulp/bin/gulp.js" "$@"
else
  exec node  "$basedir/../../../../../gulp@4.0.2/node_modules/gulp/bin/gulp.js" "$@"
fi
