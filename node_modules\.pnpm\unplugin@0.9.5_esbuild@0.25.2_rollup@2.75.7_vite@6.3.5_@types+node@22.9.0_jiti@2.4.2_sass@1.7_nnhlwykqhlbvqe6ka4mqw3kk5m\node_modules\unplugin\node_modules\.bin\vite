#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules/vite/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules/vite/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules/vite/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules/vite/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../vite@6.3.5_@types+node@22.9.0_jiti@2.4.2_sass@1.79.3_terser@5.36.0_tsx@4.19.3_yaml@2.7.1/node_modules/vite/bin/vite.js" "$@"
fi
