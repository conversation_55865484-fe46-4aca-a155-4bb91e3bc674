#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\work\vue\neue-plus\node_modules\.pnpm\mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_\node_modules\mkdist\dist\node_modules;C:\work\vue\neue-plus\node_modules\.pnpm\mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_\node_modules\mkdist\node_modules;C:\work\vue\neue-plus\node_modules\.pnpm\mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_\node_modules;C:\work\vue\neue-plus\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/cli.cjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/cli.cjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/cli.cjs" $args
  } else {
    & "node$exe"  "$basedir/../../../../../mkdist@1.5.3_sass@1.79.3_typescript@5.5.4_vue-tsc@2.2.10_typescript@5.5.4_/node_modules/mkdist/dist/cli.cjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
