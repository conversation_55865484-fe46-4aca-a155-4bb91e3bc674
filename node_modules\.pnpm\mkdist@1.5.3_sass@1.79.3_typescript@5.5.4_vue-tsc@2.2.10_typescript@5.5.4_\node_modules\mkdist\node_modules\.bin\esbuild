#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/esbuild@0.21.5/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/esbuild@0.21.5/node_modules/esbuild/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/esbuild@0.21.5/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../esbuild@0.21.5/node_modules/esbuild/bin/esbuild" "$@"
else
  exec node  "$basedir/../../../../../esbuild@0.21.5/node_modules/esbuild/bin/esbuild" "$@"
fi
