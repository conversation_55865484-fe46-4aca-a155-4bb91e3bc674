#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/cssesc@3.0.0/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/cssesc@3.0.0/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../cssesc@3.0.0/node_modules/cssesc/bin/cssesc" "$@"
else
  exec node  "$basedir/../../../../../cssesc@3.0.0/node_modules/cssesc/bin/cssesc" "$@"
fi
