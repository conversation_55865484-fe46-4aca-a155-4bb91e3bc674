#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/svgo@2.8.0/node_modules/svgo/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/svgo@2.8.0/node_modules/svgo/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/svgo@2.8.0/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/work/vue/neue-plus/node_modules/.pnpm/svgo@2.8.0/node_modules/svgo/bin/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/svgo@2.8.0/node_modules/svgo/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/svgo@2.8.0/node_modules:/mnt/c/work/vue/neue-plus/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../svgo@2.8.0/node_modules/svgo/bin/svgo" "$@"
else
  exec node  "$basedir/../../../../../svgo@2.8.0/node_modules/svgo/bin/svgo" "$@"
fi
